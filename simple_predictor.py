"""
Simple predictor that works directly with the final model
by creating a minimal preprocessing pipeline.
"""

import pandas as pd
import numpy as np
import joblib
import logging
from sklearn.preprocessing import StandardScaler
from category_encoders import TargetEncoder
from sklearn.impute import SimpleImputer
from url_feature_extractor import URLFeatureExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimplePhishingPredictor:
    """
    A simple predictor that works with the extracted features
    and makes predictions using a basic preprocessing approach.
    """
    
    def __init__(self, model_path: str = "stacking_ensemble.joblib"):
        """Initialize the predictor."""
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.target_encoder = TargetEncoder(cols=['TLD'])
        self.imputer = SimpleImputer(strategy='median')
        self.is_fitted = False
        
        # Load the trained model
        self.load_model()
    
    def load_model(self):
        """Load the trained model."""
        try:
            model_package = joblib.load(self.model_path)
            self.model = model_package['stacking_classifier']
            logger.info("✅ Model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def fit_preprocessors(self, X: pd.DataFrame, y: pd.Series = None):
        """Fit the preprocessors on sample data."""
        try:
            # Identify categorical and numerical columns
            categorical_cols = ['TLD']
            numerical_cols = [col for col in X.columns if col != 'TLD']
            
            # Fit imputer on numerical columns
            if numerical_cols:
                self.imputer.fit(X[numerical_cols])
            
            # Fit target encoder on categorical columns
            if categorical_cols and y is not None:
                self.target_encoder.fit(X, y)
            else:
                # Create dummy target for fitting
                dummy_y = pd.Series([0] * len(X), index=X.index)
                self.target_encoder.fit(X, dummy_y)
            
            # Apply initial transformations
            X_processed = X.copy()
            if numerical_cols:
                X_processed[numerical_cols] = self.imputer.transform(X_processed[numerical_cols])
            
            X_encoded = self.target_encoder.transform(X_processed)
            
            # Fit scaler
            self.scaler.fit(X_encoded)
            
            self.is_fitted = True
            logger.info("✅ Preprocessors fitted successfully")
            
        except Exception as e:
            logger.error(f"Error fitting preprocessors: {e}")
            raise
    
    def preprocess(self, X: pd.DataFrame) -> np.ndarray:
        """Preprocess features for prediction."""
        try:
            if not self.is_fitted:
                # Fit on the input data itself (not ideal but necessary)
                self.fit_preprocessors(X)
            
            # Apply transformations
            categorical_cols = ['TLD']
            numerical_cols = [col for col in X.columns if col != 'TLD']
            
            X_processed = X.copy()
            
            # Impute numerical features
            if numerical_cols:
                X_processed[numerical_cols] = self.imputer.transform(X_processed[numerical_cols])
            
            # Encode categorical features
            X_encoded = self.target_encoder.transform(X_processed)
            
            # Scale features
            X_scaled = self.scaler.transform(X_encoded)
            
            return X_scaled
            
        except Exception as e:
            logger.error(f"Error preprocessing features: {e}")
            raise
    
    def predict(self, X: pd.DataFrame) -> tuple:
        """
        Make prediction on input features.
        
        Args:
            X: DataFrame with extracted features
            
        Returns:
            Tuple of (prediction, confidence)
        """
        try:
            # Preprocess features
            X_processed = self.preprocess(X)
            
            # Make prediction
            prediction = self.model.predict(X_processed)[0]
            
            # Get confidence if possible
            try:
                probabilities = self.model.predict_proba(X_processed)[0]
                confidence = float(max(probabilities))
            except:
                confidence = None
            
            return prediction, confidence
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            raise

def test_simple_predictor():
    """Test the simple predictor."""
    
    print("=" * 60)
    print("🧪 TESTING SIMPLE PREDICTOR")
    print("=" * 60)
    
    try:
        # 1. Extract features
        print("\n1️⃣ Extracting features...")
        extractor = URLFeatureExtractor()
        features_df = extractor.extract_features("https://www.google.com")
        
        print(f"✅ Features extracted: {features_df.shape}")
        print(f"Sample values: {dict(list(features_df.iloc[0].items())[:5])}")
        
        # 2. Initialize predictor
        print("\n2️⃣ Initializing predictor...")
        predictor = SimplePhishingPredictor()
        
        # 3. Make prediction
        print("\n3️⃣ Making prediction...")
        prediction, confidence = predictor.predict(features_df)
        
        prediction_label = "Legitimate" if prediction == 0 else "Phishing"
        
        print(f"✅ Prediction: {prediction}")
        print(f"✅ Label: {prediction_label}")
        if confidence:
            print(f"✅ Confidence: {confidence:.3f}")
        
        # Test with multiple URLs
        print("\n4️⃣ Testing multiple URLs...")
        test_urls = [
            "https://www.google.com",
            "https://github.com",
            "https://stackoverflow.com"
        ]
        
        for url in test_urls:
            try:
                features_df = extractor.extract_features(url)
                prediction, confidence = predictor.predict(features_df)
                prediction_label = "Legitimate" if prediction == 0 else "Phishing"
                
                conf_str = f" (confidence: {confidence:.3f})" if confidence else ""
                print(f"   {url}: {prediction_label}{conf_str}")
                
            except Exception as e:
                print(f"   {url}: Error - {e}")
        
        print("\n🎉 SIMPLE PREDICTOR TEST SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"\n❌ SIMPLE PREDICTOR TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_predictor()
    
    if success:
        print("\n🚀 The simple predictor is working correctly!")
    else:
        print("\n❌ The simple predictor needs fixing.")
