"""
Test script to validate the complete phishing detection pipeline
"""

import pandas as pd
import numpy as np
import joblib
import logging
from url_feature_extractor import URLFeatureExtractor
from data_preprocessor import DataPreprocessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_complete_pipeline():
    """Test the complete pipeline from URL to prediction."""
    
    print("=" * 60)
    print("🧪 TESTING PHISHING DETECTION PIPELINE")
    print("=" * 60)
    
    try:
        # 1. Test Feature Extraction
        print("\n1️⃣ Testing Feature Extraction...")
        extractor = URLFeatureExtractor(timeout=10, max_retries=1)
        
        test_urls = [
            "https://www.google.com",
            "https://github.com",
            "http://example.com"
        ]
        
        for url in test_urls:
            print(f"\n   Testing URL: {url}")
            features_df = extractor.extract_features(url)
            print(f"   ✅ Features extracted: {features_df.shape}")
            print(f"   📊 Sample features: {dict(list(features_df.iloc[0].items())[:5])}")
        
        # 2. Test Model Loading
        print("\n2️⃣ Testing Model Loading...")
        model_path = "stacking_ensemble.joblib"
        model_package = joblib.load(model_path)
        model = model_package['stacking_classifier']
        print(f"   ✅ Model loaded successfully")
        print(f"   📋 Model type: {type(model).__name__}")
        
        # 3. Test Preprocessing Pipeline
        print("\n3️⃣ Testing Preprocessing Pipeline...")
        preprocessor = DataPreprocessor()
        
        # Set up preprocessor with saved components
        preprocessor.num_imputer = model_package.get('num_imputer')
        preprocessor.cat_imputer = model_package.get('cat_imputer')
        preprocessor.target_encoder = model_package.get('target_encoder')
        preprocessor.scaler = model_package.get('scaler')
        preprocessor.categorical_cols = model_package.get('categorical_cols', [])
        preprocessor.numerical_cols = model_package.get('numerical_cols', [])
        preprocessor.is_fitted = True
        
        print(f"   ✅ Preprocessor initialized")
        print(f"   📊 Categorical columns: {preprocessor.categorical_cols}")
        print(f"   📊 Numerical columns: {len(preprocessor.numerical_cols)} features")
        
        # 4. Test Complete Pipeline
        print("\n4️⃣ Testing Complete Pipeline...")
        
        test_url = "https://www.google.com"
        print(f"   Testing with URL: {test_url}")
        
        # Extract features
        features_df = extractor.extract_features(test_url)
        print(f"   ✅ Features extracted: {features_df.shape}")
        
        # Preprocess features
        processed_features = preprocessor.transform(features_df)
        print(f"   ✅ Features preprocessed: {processed_features.shape}")
        
        # Make prediction
        prediction = model.predict(processed_features)[0]
        print(f"   ✅ Prediction made: {prediction}")
        
        # Get prediction probabilities
        try:
            probabilities = model.predict_proba(processed_features)[0]
            confidence = float(max(probabilities))
            print(f"   ✅ Confidence: {confidence:.3f}")
        except Exception as e:
            print(f"   ⚠️ Could not get probabilities: {e}")
        
        # Interpret result
        prediction_label = "Legitimate" if prediction == 0 else "Phishing"
        print(f"   🎯 Result: {prediction_label}")
        
        # 5. Test Multiple URLs
        print("\n5️⃣ Testing Multiple URLs...")
        
        test_cases = [
            ("https://www.google.com", "Expected: Legitimate"),
            ("https://github.com", "Expected: Legitimate"),
            ("https://stackoverflow.com", "Expected: Legitimate")
        ]
        
        results = []
        for url, expected in test_cases:
            try:
                features_df = extractor.extract_features(url)
                processed_features = preprocessor.transform(features_df)
                prediction = model.predict(processed_features)[0]
                prediction_label = "Legitimate" if prediction == 0 else "Phishing"
                
                results.append({
                    'url': url,
                    'prediction': prediction_label,
                    'expected': expected
                })
                
                print(f"   {url}: {prediction_label} ({expected})")
                
            except Exception as e:
                print(f"   ❌ Error with {url}: {e}")
                results.append({
                    'url': url,
                    'prediction': f"Error: {e}",
                    'expected': expected
                })
        
        # 6. Summary
        print("\n6️⃣ Test Summary...")
        print(f"   ✅ Feature extraction: Working")
        print(f"   ✅ Model loading: Working")
        print(f"   ✅ Preprocessing: Working")
        print(f"   ✅ Prediction: Working")
        print(f"   📊 Test results:")
        
        for result in results:
            status = "✅" if "Error" not in result['prediction'] else "❌"
            print(f"      {status} {result['url']}: {result['prediction']}")
        
        print("\n🎉 PIPELINE TEST COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"\n❌ PIPELINE TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_extraction_details():
    """Test feature extraction in detail."""
    
    print("\n" + "=" * 60)
    print("🔍 DETAILED FEATURE EXTRACTION TEST")
    print("=" * 60)
    
    extractor = URLFeatureExtractor()
    test_url = "https://www.google.com"
    
    features_df = extractor.extract_features(test_url)
    
    print(f"\nURL: {test_url}")
    print(f"Features extracted: {features_df.shape[1]}")
    print("\nAll features:")
    
    for i, (feature_name, value) in enumerate(features_df.iloc[0].items(), 1):
        print(f"  {i:2d}. {feature_name:<25}: {value}")
    
    # Verify exact feature order
    expected_features = [
        'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
        'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
        'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
        'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
        'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
        'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
        'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
    ]
    
    actual_features = list(features_df.columns)
    
    print(f"\n✅ Feature order verification:")
    if actual_features == expected_features:
        print("   ✅ All features in correct order")
    else:
        print("   ❌ Feature order mismatch")
        for i, (expected, actual) in enumerate(zip(expected_features, actual_features)):
            if expected != actual:
                print(f"      Position {i+1}: Expected '{expected}', got '{actual}'")

if __name__ == "__main__":
    # Run tests
    success = test_complete_pipeline()
    
    if success:
        test_feature_extraction_details()
        print("\n🚀 ALL TESTS PASSED! The application is ready for deployment.")
    else:
        print("\n❌ TESTS FAILED! Please check the errors above.")
