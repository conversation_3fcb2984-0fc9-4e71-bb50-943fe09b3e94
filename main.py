"""
FastAPI Application for Phishing URL Detection

This application provides a REST API for detecting phishing URLs using a pre-trained
stacking ensemble model. It extracts 30 features from URLs and applies the exact
preprocessing pipeline used during training.

Endpoints:
- GET /: Serve the web interface
- POST /predict: Predict if a URL is phishing or legitimate
- GET /health: Health check endpoint
"""

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
import joblib
import pandas as pd
import numpy as np
import logging
import traceback
from typing import Dict, Any, Optional
import os
from pathlib import Path

from url_feature_extractor import URLFeatureExtractor
from simple_predictor import SimplePhishingPredictor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Phishing URL Detection API",
    description="API for detecting phishing URLs using machine learning",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and components
predictor = None
feature_extractor = None

class URLRequest(BaseModel):
    """Request model for URL prediction."""
    url: str
    
    @validator('url')
    def validate_url(cls, v):
        if not v or not v.strip():
            raise ValueError('URL cannot be empty')
        
        # Basic URL validation
        url = v.strip()
        if not any(url.startswith(prefix) for prefix in ['http://', 'https://', 'www.', 'ftp://']):
            # Add http:// if no protocol specified
            if not url.startswith(('www.', 'ftp://')):
                url = 'http://' + url
            elif url.startswith('www.'):
                url = 'http://' + url
        
        return url

class PredictionResponse(BaseModel):
    """Response model for URL prediction."""
    url: str
    prediction: int  # 0 = Legitimate, 1 = Phishing
    prediction_label: str
    confidence: Optional[float] = None
    features_extracted: bool
    processing_time_ms: float
    message: str

@app.on_event("startup")
async def startup_event():
    """Load model and initialize components on startup."""
    global predictor, feature_extractor

    try:
        logger.info("Starting up Phishing Detection API...")

        # Initialize feature extractor
        feature_extractor = URLFeatureExtractor(timeout=10, max_retries=2)
        logger.info("✅ Feature extractor initialized")

        # Initialize predictor
        model_path = "stacking_ensemble.joblib"
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")

        predictor = SimplePhishingPredictor(model_path)
        logger.info("✅ Predictor initialized")

        # Create static directory if it doesn't exist
        static_dir = Path("static")
        static_dir.mkdir(exist_ok=True)

        logger.info("🚀 Phishing Detection API startup completed successfully!")

    except Exception as e:
        logger.error(f"❌ Startup failed: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@app.get("/", response_class=HTMLResponse)
async def serve_web_interface():
    """Serve the main web interface."""
    try:
        html_file = Path("static/index.html")
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(), status_code=200)
        else:
            # Return a simple HTML page if static file doesn't exist
            return HTMLResponse(content="""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Phishing URL Detection</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 600px; margin: 0 auto; }
                    input[type="text"] { width: 100%; padding: 10px; margin: 10px 0; }
                    button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
                    .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
                    .legitimate { background: #d4edda; color: #155724; }
                    .phishing { background: #f8d7da; color: #721c24; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Phishing URL Detection</h1>
                    <p>Enter a URL to check if it's legitimate or phishing:</p>
                    <input type="text" id="urlInput" placeholder="Enter URL (e.g., https://example.com)">
                    <button onclick="checkURL()">Check URL</button>
                    <div id="result"></div>
                </div>
                <script>
                    async function checkURL() {
                        const url = document.getElementById('urlInput').value;
                        const resultDiv = document.getElementById('result');
                        
                        if (!url) {
                            resultDiv.innerHTML = '<div class="result">Please enter a URL</div>';
                            return;
                        }
                        
                        resultDiv.innerHTML = '<div class="result">Analyzing URL...</div>';
                        
                        try {
                            const response = await fetch('/predict', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ url: url })
                            });
                            
                            const data = await response.json();
                            
                            if (response.ok) {
                                const className = data.prediction === 0 ? 'legitimate' : 'phishing';
                                resultDiv.innerHTML = `
                                    <div class="result ${className}">
                                        <strong>Result: ${data.prediction_label}</strong><br>
                                        URL: ${data.url}<br>
                                        Processing time: ${data.processing_time_ms.toFixed(2)}ms
                                    </div>
                                `;
                            } else {
                                resultDiv.innerHTML = `<div class="result">Error: ${data.detail}</div>`;
                            }
                        } catch (error) {
                            resultDiv.innerHTML = `<div class="result">Error: ${error.message}</div>`;
                        }
                    }
                    
                    document.getElementById('urlInput').addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            checkURL();
                        }
                    });
                </script>
            </body>
            </html>
            """, status_code=200)
    except Exception as e:
        logger.error(f"Error serving web interface: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/predict", response_model=PredictionResponse)
async def predict_url(request: URLRequest):
    """
    Predict if a URL is phishing or legitimate.
    
    Args:
        request: URLRequest containing the URL to analyze
        
    Returns:
        PredictionResponse with prediction results
    """
    import time
    start_time = time.time()
    
    try:
        logger.info(f"Received prediction request for URL: {request.url}")
        
        if not predictor or not feature_extractor:
            raise HTTPException(status_code=500, detail="Model not properly initialized")

        # Extract features from URL
        features_df = feature_extractor.extract_features(request.url)
        features_extracted = True

        logger.info(f"Features extracted successfully. Shape: {features_df.shape}")

        # Make prediction using the simple predictor
        prediction, confidence = predictor.predict(features_df)

        logger.info(f"Prediction made: {prediction}, confidence: {confidence}")
        
        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000
        
        # Create response
        prediction_label = "Legitimate" if prediction == 0 else "Phishing"
        
        response = PredictionResponse(
            url=request.url,
            prediction=int(prediction),
            prediction_label=prediction_label,
            confidence=confidence,
            features_extracted=features_extracted,
            processing_time_ms=processing_time_ms,
            message=f"URL classified as {prediction_label.lower()}"
        )
        
        logger.info(f"Prediction completed: {prediction_label} (confidence: {confidence})")
        return response
        
    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        logger.error(f"Error during prediction: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Return error response
        raise HTTPException(
            status_code=500,
            detail=f"Prediction failed: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "predictor_loaded": predictor is not None,
        "feature_extractor_loaded": feature_extractor is not None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
