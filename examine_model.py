"""
Examine the trained model to understand expected features
"""

import joblib
import pandas as pd

def examine_model():
    """Examine the model package to understand expected features."""
    
    print("=" * 60)
    print("🔍 EXAMINING TRAINED MODEL")
    print("=" * 60)
    
    # Load the model package
    model_package = joblib.load('stacking_ensemble.joblib')
    
    print("\n1️⃣ Model Package Contents:")
    for key in model_package.keys():
        print(f"   - {key}: {type(model_package[key])}")
    
    # Get feature information
    numerical_cols = model_package.get('numerical_cols', [])
    categorical_cols = model_package.get('categorical_cols', [])
    selected_features = model_package.get('selected_features', [])
    feature_names_after_var = model_package.get('feature_names_after_var', [])
    
    print(f"\n2️⃣ Feature Information:")
    print(f"   Total numerical features: {len(numerical_cols)}")
    print(f"   Total categorical features: {len(categorical_cols)}")
    print(f"   Selected features: {len(selected_features) if selected_features else 'None'}")
    print(f"   Features after variance selection: {len(feature_names_after_var) if feature_names_after_var else 'None'}")
    
    print(f"\n3️⃣ All Expected Features ({len(numerical_cols + categorical_cols)} total):")
    all_features = numerical_cols + categorical_cols
    for i, feature in enumerate(all_features, 1):
        print(f"   {i:2d}. {feature}")
    
    if selected_features:
        print(f"\n4️⃣ Selected Features ({len(selected_features)} total):")
        for i, feature in enumerate(selected_features, 1):
            print(f"   {i:2d}. {feature}")
    
    print(f"\n5️⃣ Categorical Features:")
    for feature in categorical_cols:
        print(f"   - {feature}")
    
    # Check if we have the selected features CSV
    try:
        selected_df = pd.read_csv('selected_features (2).csv')
        print(f"\n6️⃣ Features from CSV file ({len(selected_df)} total):")
        for i, row in selected_df.iterrows():
            print(f"   {row['order']:2d}. {row['feature_name']}")
    except FileNotFoundError:
        print("\n6️⃣ No selected_features.csv file found")
    
    return all_features, selected_features

if __name__ == "__main__":
    all_features, selected_features = examine_model()
