"""
Model-Compatible Data Preprocessor for Phishing Detection

This preprocessor works with the existing trained model by applying the exact
same preprocessing pipeline that was used during training, including feature selection.
"""

import pandas as pd
import numpy as np
import joblib
import logging
from typing import Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelCompatiblePreprocessor:
    """
    Preprocessor that applies the exact same pipeline used during model training.
    
    This includes:
    1. Missing value imputation
    2. Target encoding for categorical features
    3. Variance threshold feature selection
    4. Standard scaling
    5. SelectKBest feature selection
    """
    
    def __init__(self, model_package_path: str = "stacking_ensemble.joblib"):
        """
        Initialize the preprocessor with the trained model components.
        
        Args:
            model_package_path: Path to the saved model package
        """
        self.model_package_path = model_package_path
        self.load_model_components()
    
    def load_model_components(self):
        """Load all preprocessing components from the trained model."""
        
        try:
            logger.info("Loading model components...")
            
            # Load the model package
            model_package = joblib.load(self.model_package_path)
            
            # Extract preprocessing components
            self.num_imputer = model_package.get('num_imputer')
            self.cat_imputer = model_package.get('cat_imputer')
            self.target_encoder = model_package.get('target_encoder')
            self.variance_selector = model_package.get('variance_selector')
            self.scaler = model_package.get('scaler')
            self.feature_selector = model_package.get('feature_selector')
            
            # Extract feature information
            self.categorical_cols = model_package.get('categorical_cols', [])
            self.numerical_cols = model_package.get('numerical_cols', [])
            self.selected_features = model_package.get('selected_features', [])
            self.feature_names_after_var = model_package.get('feature_names_after_var', [])
            
            # The model expects these 30 selected features
            self.expected_input_features = [
                'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
                'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
                'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
                'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
                'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
                'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
                'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
            ]
            
            logger.info("✅ Model components loaded successfully")
            logger.info(f"   Expected input features: {len(self.expected_input_features)}")
            logger.info(f"   Final selected features: {len(self.selected_features)}")
            
        except Exception as e:
            logger.error(f"Error loading model components: {str(e)}")
            raise
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using the exact same pipeline as training.
        
        Args:
            X: DataFrame with the 30 expected features
            
        Returns:
            Transformed DataFrame ready for model prediction
        """
        try:
            logger.info("Transforming features with model-compatible pipeline...")
            
            # Validate input features
            self._validate_input_features(X)
            
            # Since the input already contains only the selected features,
            # we need to create a full feature set that matches what the
            # original pipeline expects, then apply the same transformations
            
            # Create a DataFrame with all original features, filling missing ones with defaults
            X_full = self._create_full_feature_set(X)
            
            # Apply the same preprocessing steps as training
            X_processed = self._apply_training_pipeline(X_full)
            
            logger.info(f"✅ Features transformed successfully: {X_processed.shape}")
            return X_processed
            
        except Exception as e:
            logger.error(f"Error transforming features: {str(e)}")
            raise
    
    def _validate_input_features(self, X: pd.DataFrame):
        """Validate that input DataFrame has expected features."""
        
        missing_features = set(self.expected_input_features) - set(X.columns)
        if missing_features:
            raise ValueError(f"Missing expected features: {missing_features}")
        
        # Ensure correct column order
        X_reordered = X[self.expected_input_features]
        return X_reordered
    
    def _create_full_feature_set(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Create a full feature set that matches the original training data structure.
        
        The model was trained on 62 features, but we only extract 30.
        We need to fill in the missing 32 features with default values.
        """
        
        # All original features (62 total)
        all_original_features = self.numerical_cols + self.categorical_cols
        
        # Create a new DataFrame with all original features
        X_full = pd.DataFrame(index=X.index)
        
        # Copy the features we have
        for feature in self.expected_input_features:
            if feature in X.columns:
                X_full[feature] = X[feature]
        
        # Fill missing features with default values
        for feature in all_original_features:
            if feature not in X_full.columns:
                if feature in self.categorical_cols:
                    # For categorical features, use the most common value or 'other'
                    X_full[feature] = 'other' if feature == 'TLD' else 'unknown'
                else:
                    # For numerical features, use 0 as default
                    X_full[feature] = 0
        
        # Ensure correct column order
        X_full = X_full[all_original_features]
        
        return X_full
    
    def _apply_training_pipeline(self, X_full: pd.DataFrame) -> pd.DataFrame:
        """Apply the exact same preprocessing pipeline used during training."""
        
        # Step 1: Missing value imputation
        X_imputed = X_full.copy()
        
        if self.num_imputer and self.numerical_cols:
            X_imputed[self.numerical_cols] = self.num_imputer.transform(X_imputed[self.numerical_cols])
        
        if self.cat_imputer and self.categorical_cols:
            X_imputed[self.categorical_cols] = self.cat_imputer.transform(X_imputed[self.categorical_cols])
        
        # Step 2: Target encoding for categorical features
        if self.target_encoder and self.categorical_cols:
            X_encoded = self.target_encoder.transform(X_imputed)
        else:
            X_encoded = X_imputed
        
        # Step 3: Variance threshold feature selection
        if self.variance_selector:
            X_var_selected = self.variance_selector.transform(X_encoded)
            # Convert back to DataFrame with correct feature names
            X_var_selected = pd.DataFrame(
                X_var_selected, 
                columns=self.feature_names_after_var,
                index=X_encoded.index
            )
        else:
            X_var_selected = X_encoded
        
        # Step 4: Standard scaling
        if self.scaler:
            X_scaled = self.scaler.transform(X_var_selected)
            # Convert back to DataFrame
            X_scaled = pd.DataFrame(
                X_scaled,
                columns=X_var_selected.columns,
                index=X_var_selected.index
            )
        else:
            X_scaled = X_var_selected
        
        # Step 5: SelectKBest feature selection
        if self.feature_selector:
            X_final = self.feature_selector.transform(X_scaled)
            # Convert back to DataFrame with selected feature names
            X_final = pd.DataFrame(
                X_final,
                columns=self.selected_features,
                index=X_scaled.index
            )
        else:
            X_final = X_scaled
        
        return X_final


# Example usage and testing
if __name__ == "__main__":
    from url_feature_extractor import URLFeatureExtractor
    
    # Test the model-compatible preprocessor
    print("Testing Model-Compatible Preprocessor...")
    
    # Extract features from a test URL
    extractor = URLFeatureExtractor()
    features_df = extractor.extract_features("https://www.google.com")
    
    print(f"Original features shape: {features_df.shape}")
    print(f"Feature columns: {list(features_df.columns)}")
    
    # Test preprocessor
    preprocessor = ModelCompatiblePreprocessor()
    
    # Transform features
    processed_features = preprocessor.transform(features_df)
    
    print(f"Processed features shape: {processed_features.shape}")
    print(f"Processed feature columns: {list(processed_features.columns)}")
    print(f"Sample processed values: {processed_features.iloc[0].head(10).to_dict()}")
    
    # Test with model prediction
    try:
        model_package = joblib.load("stacking_ensemble.joblib")
        model = model_package['stacking_classifier']
        
        prediction = model.predict(processed_features)[0]
        probabilities = model.predict_proba(processed_features)[0]
        
        print(f"Prediction: {prediction}")
        print(f"Probabilities: {probabilities}")
        print(f"Confidence: {max(probabilities):.3f}")
        
        prediction_label = "Legitimate" if prediction == 0 else "Phishing"
        print(f"Result: {prediction_label}")
        
    except Exception as e:
        print(f"Error making prediction: {e}")
