/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    text-align: center;
    padding: 60px 0 40px;
    color: white;
}

.header-content .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.header-content .logo i {
    font-size: 2.5rem;
    color: #ffd700;
}

.header-content h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin: 0;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main-content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 40px;
}

/* Input Section */
.input-section {
    padding: 50px 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    text-align: center;
}

.input-container h2 {
    font-size: 2.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.description {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.url-form {
    max-width: 700px;
    margin: 0 auto;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: stretch;
}

.input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 20px;
    color: #6c757d;
    font-size: 1.1rem;
    z-index: 2;
}

#urlInput {
    width: 100%;
    padding: 18px 20px 18px 55px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    outline: none;
}

#urlInput:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-btn {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0;
    visibility: hidden;
}

.clear-btn:hover {
    background: #f8f9fa;
    color: #dc3545;
}

.clear-btn.visible {
    opacity: 1;
    visibility: visible;
}

.analyze-btn {
    padding: 18px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    white-space: nowrap;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.analyze-btn:active {
    transform: translateY(0);
}

.analyze-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 30px;
    margin-top: 30px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Results Section */
.results-section {
    padding: 40px;
    border-top: 1px solid #e9ecef;
}

.result-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.result-card {
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.result-card.legitimate {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    color: #155724;
}

.result-card.phishing {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 2px solid #dc3545;
    color: #721c24;
}

.result-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.result-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.result-url {
    font-size: 1rem;
    word-break: break-all;
    margin-bottom: 15px;
    opacity: 0.8;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.detail-item {
    background: rgba(255, 255, 255, 0.3);
    padding: 10px;
    border-radius: 8px;
}

.detail-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.detail-value {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Features Section */
.features-section {
    padding: 60px 40px;
    background: #f8f9fa;
    text-align: center;
}

.features-section h3 {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 40px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    padding: 30px 20px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.5rem;
}

.feature-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.feature-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Examples Section */
.examples-section {
    padding: 40px;
    text-align: center;
}

.examples-section h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 30px;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.example-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    text-align: left;
}

.example-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.example-icon {
    font-size: 1.2rem;
}

.example-icon.safe {
    color: #28a745;
}

.example-item span {
    flex: 1;
    font-weight: 500;
    word-break: break-all;
}

.example-item small {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    text-align: center;
    padding: 30px 0;
    color: white;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-link {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-link:hover {
    opacity: 1;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toast {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    border-left: 4px solid #dc3545;
}

.toast.success {
    border-left: 4px solid #28a745;
}

.toast.info {
    border-left: 4px solid #17a2b8;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header {
        padding: 40px 0 30px;
    }
    
    .header-content h1 {
        font-size: 2.2rem;
    }
    
    .input-section {
        padding: 40px 20px;
    }
    
    .input-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .analyze-btn {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .examples-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        gap: 10px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
