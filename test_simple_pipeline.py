"""
Simple test of the preprocessing pipeline
"""

import pandas as pd
import numpy as np
import joblib
import logging
from url_feature_extractor import URLFeatureExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def preprocess_features(features_df: pd.DataFrame, model_components: dict) -> pd.DataFrame:
    """
    Apply the exact preprocessing pipeline used during training.
    
    Args:
        features_df: DataFrame with extracted features
        model_components: Dictionary with preprocessing components
        
    Returns:
        Preprocessed features ready for model prediction
    """
    try:
        # The features_df already contains the 30 selected features
        # We need to apply the preprocessing steps in the correct order
        
        # Get the preprocessing components
        num_imputer = model_components.get('num_imputer')
        cat_imputer = model_components.get('cat_imputer')
        target_encoder = model_components.get('target_encoder')
        scaler = model_components.get('scaler')
        
        # Get feature information
        categorical_cols = ['TLD']  # Only TLD is categorical in our feature set
        numerical_cols = [col for col in features_df.columns if col != 'TLD']
        
        print(f"Categorical columns: {categorical_cols}")
        print(f"Numerical columns: {len(numerical_cols)} features")
        
        # Step 1: Missing value imputation
        X_processed = features_df.copy()
        
        if num_imputer and numerical_cols:
            X_processed[numerical_cols] = num_imputer.transform(X_processed[numerical_cols])
            print("✅ Numerical imputation applied")
        
        if cat_imputer and categorical_cols:
            X_processed[categorical_cols] = cat_imputer.transform(X_processed[categorical_cols])
            print("✅ Categorical imputation applied")
        
        # Step 2: Target encoding for categorical features
        if target_encoder and categorical_cols:
            X_processed = target_encoder.transform(X_processed)
            print("✅ Target encoding applied")
        
        # Step 3: Standard scaling
        if scaler:
            X_scaled = scaler.transform(X_processed)
            # Convert back to DataFrame with correct column names
            X_processed = pd.DataFrame(X_scaled, columns=features_df.columns, index=features_df.index)
            print("✅ Standard scaling applied")
        
        return X_processed
        
    except Exception as e:
        print(f"Error in preprocessing: {str(e)}")
        raise

def test_pipeline():
    """Test the complete pipeline."""
    
    print("=" * 60)
    print("🧪 TESTING SIMPLE PIPELINE")
    print("=" * 60)
    
    try:
        # 1. Extract features
        print("\n1️⃣ Extracting features...")
        extractor = URLFeatureExtractor()
        features_df = extractor.extract_features("https://www.google.com")
        
        print(f"✅ Features extracted: {features_df.shape}")
        print(f"Feature columns: {list(features_df.columns)}")
        print(f"Sample values: {dict(list(features_df.iloc[0].items())[:5])}")
        
        # 2. Load model components
        print("\n2️⃣ Loading model components...")
        model_components = joblib.load("stacking_ensemble.joblib")
        model = model_components['stacking_classifier']
        
        print(f"✅ Model loaded: {type(model).__name__}")
        
        # 3. Preprocess features
        print("\n3️⃣ Preprocessing features...")
        processed_features = preprocess_features(features_df, model_components)
        
        print(f"✅ Features preprocessed: {processed_features.shape}")
        print(f"Sample processed values: {dict(list(processed_features.iloc[0].items())[:5])}")
        
        # 4. Make prediction
        print("\n4️⃣ Making prediction...")
        prediction = model.predict(processed_features)[0]
        
        try:
            probabilities = model.predict_proba(processed_features)[0]
            confidence = float(max(probabilities))
            print(f"✅ Prediction: {prediction}")
            print(f"✅ Probabilities: {probabilities}")
            print(f"✅ Confidence: {confidence:.3f}")
        except Exception as e:
            print(f"⚠️ Could not get probabilities: {e}")
            print(f"✅ Prediction: {prediction}")
        
        # Interpret result
        prediction_label = "Legitimate" if prediction == 0 else "Phishing"
        print(f"🎯 Result: {prediction_label}")
        
        print("\n🎉 PIPELINE TEST SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"\n❌ PIPELINE TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pipeline()
    
    if success:
        print("\n🚀 The pipeline is working correctly!")
    else:
        print("\n❌ The pipeline needs fixing.")
