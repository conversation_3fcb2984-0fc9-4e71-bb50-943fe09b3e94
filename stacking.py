import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import <PERSON><PERSON><PERSON><PERSON><PERSON>eshold, SelectKBest, mutual_info_classif
from sklearn.ensemble import StackingClassifier, RandomForestClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.kernel_approximation import RBFSampler
from sklearn.pipeline import Pipeline
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,
                           roc_auc_score, confusion_matrix, roc_curve,
                           classification_report)
from sklearn.compose import ColumnTransformer
from category_encoders import TargetEncoder

from lightgbm import LGBMClassifier
from xgboost import XGBClassifier
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import gmean
import time

# Set random seed for reproducibility
np.random.seed(42)

print("=" * 60)
print("🎯 PHISHING URL DETECTION - STACKING ENSEMBLE CLASSIFIER")
print("=" * 60)

# =============================================================================
# 1️⃣ LOAD & SPLIT DATASET
# =============================================================================
print("\n" + "=" * 40)
print("📊 LOADING DATASET")
print("=" * 40)

# Load the dataset
try:
    df = pd.read_csv('StealthPhisher2025.csv')
    print(f"✅ Dataset loaded successfully!")
    print(f"📏 Dataset shape: {df.shape}")
    print(f"📋 Columns: {list(df.columns)}")
except FileNotFoundError:
    print("❌ Error: StealthPhisher2025.csv not found!")
    exit()

# Display basic info
print(f"\n📈 Target distribution:")
if 'Label' in df.columns:
    print(df['Label'].value_counts())
else:
    print("❌ 'Label' column not found!")

# Drop URL and Domain columns if they exist
columns_to_drop = ['URL', 'Domain']
existing_cols_to_drop = [col for col in columns_to_drop if col in df.columns]
if existing_cols_to_drop:
    df = df.drop(columns=existing_cols_to_drop)
    print(f"🗑️ Dropped columns: {existing_cols_to_drop}")

# Encode Label as binary using map() - Explicitly: Legitimate to 0, Phishing to 1
if 'Label' in df.columns:
    unique_labels = df['Label'].unique()
    print(f"📋 Unique labels before encoding: {unique_labels}")

    # Explicit mapping: Legitimate -> 0, Phishing -> 1
    label_map = {'Legitimate': 0, 'Phishing': 1}
    df['Label'] = df['Label'].map(label_map)

    # Check if mapping was successful
    if df['Label'].isnull().any():
        print("⚠️ Warning: Some labels could not be mapped. Checking for different label formats...")
        # Handle potential variations in label naming
        unique_labels_lower = [str(label).lower() for label in unique_labels]
        if 'legitimate' in unique_labels_lower and 'phishing' in unique_labels_lower:
            # Case-insensitive mapping
            df['Label'] = df['Label'].astype(str).str.lower().map({'legitimate': 0, 'phishing': 1})
            print("🔄 Applied case-insensitive mapping")
        elif set(unique_labels) == {0, 1}:
            print("✅ Labels already encoded as 0/1")
        else:
            print(f"❌ Unexpected label format: {unique_labels}")
            print("🔄 Attempting automatic mapping...")
            # Fallback: map first unique label to 0, second to 1
            label_map = {unique_labels[0]: 0, unique_labels[1]: 1}
            df['Label'] = df['Label'].map(label_map)

    print(f"🔄 Label mapping applied: {label_map}")
    print(f"📊 Label distribution after encoding: {df['Label'].value_counts().to_dict()}")
else:
    print("❌ 'Label' column not found!")
    exit()

# Separate features and target
X = df.drop('Label', axis=1)
y = df['Label']

print(f"\n📊 Final dataset info:")
print(f"   Features shape: {X.shape}")
print(f"   Target shape: {y.shape}")
print(f"   Target distribution: {y.value_counts().to_dict()}")

# Split dataset (stratified, 80/20) BEFORE preprocessing
print("\n" + "=" * 40)
print("✂️ SPLITTING DATASET")
print("=" * 40)

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, stratify=y, random_state=42
)

print(f"📊 Training set: {X_train.shape[0]} samples")
print(f"📊 Test set: {X_test.shape[0]} samples")
print(f"📈 Train target distribution: {y_train.value_counts().to_dict()}")
print(f"📈 Test target distribution: {y_test.value_counts().to_dict()}")

# =============================================================================
# 2️⃣ DATA PREPROCESSING (Outside Final Pipeline)
# =============================================================================
print("\n" + "=" * 40)
print("🔧 DATA PREPROCESSING")
print("=" * 40)

# Identify categorical and numerical columns
categorical_cols = X_train.select_dtypes(include=['object', 'category']).columns.tolist()
numerical_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()

print(f"📋 Categorical columns ({len(categorical_cols)}): {categorical_cols[:5]}...")
print(f"📋 Numerical columns ({len(numerical_cols)}): {numerical_cols[:5]}...")

# 2.1 Handle Missing Values
print("\n--- Handling Missing Values ---")
missing_train = X_train.isnull().sum()
missing_test = X_test.isnull().sum()

print(f"🔍 Missing values in training set: {missing_train[missing_train > 0].sum()}")
print(f"🔍 Missing values in test set: {missing_test[missing_test > 0].sum()}")

# Impute numerical features with median
if numerical_cols:
    num_imputer = SimpleImputer(strategy='median')
    X_train[numerical_cols] = num_imputer.fit_transform(X_train[numerical_cols])
    X_test[numerical_cols] = num_imputer.transform(X_test[numerical_cols])
    print("✅ Numerical features imputed with median")

# Impute categorical features with most frequent
if categorical_cols:
    cat_imputer = SimpleImputer(strategy='most_frequent')
    X_train[categorical_cols] = cat_imputer.fit_transform(X_train[categorical_cols])
    X_test[categorical_cols] = cat_imputer.transform(X_test[categorical_cols])
    print("✅ Categorical features imputed with most frequent")

# 2.2 Target Encoding for Categorical Features
print("\n--- Target Encoding ---")
if categorical_cols:
    target_encoder = TargetEncoder(cols=categorical_cols, smoothing=1.0)
    X_train_encoded = target_encoder.fit_transform(X_train, y_train)
    X_test_encoded = target_encoder.transform(X_test)
    print(f"✅ Target encoding applied to {len(categorical_cols)} categorical features")
else:
    X_train_encoded = X_train.copy()
    X_test_encoded = X_test.copy()
    print("ℹ️ No categorical features to encode")

# 2.3 Feature Selection - Variance Threshold (before scaling)
print("\n--- Feature Selection: Variance Threshold ---")
variance_selector = VarianceThreshold(threshold=0.01)
X_train_var = variance_selector.fit_transform(X_train_encoded)
X_test_var = variance_selector.transform(X_test_encoded)

# Get feature names after variance selection
feature_names_after_var = X_train_encoded.columns[variance_selector.get_support()].tolist()
print(f"✅ Features after variance threshold: {X_train_var.shape[1]} (removed {X_train_encoded.shape[1] - X_train_var.shape[1]})")

# 2.4 Scaling
print("\n--- Scaling Features ---")
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train_var)
X_test_scaled = scaler.transform(X_test_var)
print("✅ Features scaled using StandardScaler")

# 2.5 Feature Selection - SelectKBest (after scaling)
print("\n--- Feature Selection: SelectKBest ---")
k_best = min(30, X_train_scaled.shape[1])  # Ensure k doesn't exceed available features
selector = SelectKBest(score_func=mutual_info_classif, k=k_best)
X_train_final = selector.fit_transform(X_train_scaled, y_train)
X_test_final = selector.transform(X_test_scaled)

# Get final selected feature names
selected_feature_indices = selector.get_support(indices=True)
selected_features = [feature_names_after_var[i] for i in selected_feature_indices]

print(f"✅ Final features selected: {X_train_final.shape[1]}")
print(f"📋 Selected features: {selected_features[:5]}...")

# Convert back to DataFrame for consistency
X_train_final = pd.DataFrame(X_train_final, columns=selected_features, index=X_train.index)
X_test_final = pd.DataFrame(X_test_final, columns=selected_features, index=X_test.index)

# Save selected features
selected_features_df = pd.DataFrame({
    'feature_name': selected_features,
    'order': range(len(selected_features))
})
selected_features_df.to_csv('selected_features.csv', index=False)
print("💾 Selected features saved to 'selected_features.csv'")

# Save descriptive statistics
desc_stats = X_train_final.describe()
desc_stats.to_csv('descriptive_statistics.csv')
print("💾 Descriptive statistics saved to 'descriptive_statistics.csv'")

print(f"\n📊 Final preprocessed data:")
print(f"   Training features: {X_train_final.shape}")
print(f"   Test features: {X_test_final.shape}")

# =============================================================================
# 3️⃣ DEFINE BASE MODELS WITH ACCELERATED CONFIGURATIONS
# =============================================================================
print("\n" + "=" * 40)
print("🤖 DEFINING BASE MODELS")
print("=" * 40)

# Calculate scale_pos_weight for XGBoost
pos_weight = (y_train == 0).sum() / (y_train == 1).sum()
print(f"📊 Positive class weight for XGBoost: {pos_weight:.3f}")

# Base models with accelerated configurations
base_models = {
    'lgbm': {
        'model': LGBMClassifier(
            boosting_type='goss',
            is_unbalance=True,
            random_state=42,
            verbose=-1,
            n_jobs=-1
        ),
        'params': {
            'n_estimators': [100, 200],
            'max_depth': [3, 5],
            'learning_rate': [0.1, 0.2],
            'num_leaves': [31, 50]
        }
    },
    'xgb': {
        'model': XGBClassifier(
            tree_method='approx',
            scale_pos_weight=pos_weight,
            enable_categorical=False,
            random_state=42,
            n_jobs=-1,
            verbosity=0
        ),
        'params': {
            'n_estimators': [100, 200],
            'max_depth': [3, 5],
            'learning_rate': [0.1, 0.2],
            'subsample': [0.8, 1.0]
        }
    },
    'rf': {
        'model': RandomForestClassifier(
            class_weight='balanced',
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        ),
        'params': {
            'n_estimators': [100, 200],
            'max_depth': [10, 15, None],
            'min_samples_split': [2, 5]
        }
    },
    'et': {
        'model': ExtraTreesClassifier(
            class_weight='balanced',
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        ),
        'params': {
            'n_estimators': [100, 200],
            'max_depth': [10, 15, None],
            'min_samples_split': [2, 5]
        }
    }
}

print("✅ Base models defined with accelerated configurations")

# =============================================================================
# 4️⃣ HYPERPARAMETER TUNING & TRAINING BASE MODELS
# =============================================================================
print("\n" + "=" * 40)
print("🎯 HYPERPARAMETER TUNING & TRAINING")
print("=" * 40)

# Cross-validation setup
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
tuned_models = {}
base_metrics = []

def calculate_gmean(y_true, y_pred):
    """Calculate G-Mean (Geometric Mean of Sensitivity and Specificity)"""
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, fn, tp = cm.ravel()
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    return np.sqrt(sensitivity * specificity)

def evaluate_model(name, model, X_test, y_test):
    """Evaluate model and return metrics"""
    start_time = time.time()
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    eval_time = time.time() - start_time

    return {
        'Model': name,
        'Accuracy': accuracy_score(y_test, y_pred),
        'Precision': precision_score(y_test, y_pred),
        'Recall': recall_score(y_test, y_pred),
        'F1': f1_score(y_test, y_pred),
        'ROC_AUC': roc_auc_score(y_test, y_pred_proba),
        'G_Mean': calculate_gmean(y_test, y_pred),
        'Evaluation_Time': eval_time
    }

# Tune each base model sequentially
for model_name, model_config in base_models.items():
    print(f"\n=== Tuning {model_name.upper()} ===")
    start_time = time.time()

    # Grid search with cross-validation
    grid_search = GridSearchCV(
        estimator=model_config['model'],
        param_grid=model_config['params'],
        cv=cv,
        scoring='roc_auc',
        n_jobs=-1,
        verbose=0
    )

    # Fit on training data only
    grid_search.fit(X_train_final, y_train)

    # Store best model
    tuned_models[model_name] = grid_search.best_estimator_

    tuning_time = time.time() - start_time
    print(f"✅ {model_name.upper()} tuned in {tuning_time:.2f}s")
    print(f"🎯 Best score: {grid_search.best_score_:.4f}")
    print(f"🔧 Best params: {grid_search.best_params_}")

    # Evaluate on test set (NO DATA LEAKAGE - only for metrics)
    metrics = evaluate_model(model_name.upper(), grid_search.best_estimator_, X_test_final, y_test)
    base_metrics.append(metrics)

    print(f"📊 Test Accuracy: {metrics['Accuracy']:.4f}")
    print(f"📊 Test ROC-AUC: {metrics['ROC_AUC']:.4f}")

# Save base model metrics
base_metrics_df = pd.DataFrame(base_metrics)
base_metrics_df.to_csv('base_metrics.csv', index=False)
print(f"\n💾 Base model metrics saved to 'base_metrics.csv'")

# =============================================================================
# 5️⃣ STACKING ENSEMBLE WITH META LEARNER
# =============================================================================
print("\n" + "=" * 40)
print("🏗️ BUILDING STACKING ENSEMBLE")
print("=" * 40)

# Prepare base estimators for stacking
base_estimators = [(name, model) for name, model in tuned_models.items()]

# Meta learner with RBF kernel approximation for non-linearity
print("\n--- Defining Meta Learner ---")
meta_learner = Pipeline([
    ('rbf', RBFSampler(n_components=50, random_state=42)),
    ('lr', LogisticRegression(
        solver='saga',
        penalty='l2',
        class_weight='balanced',
        random_state=42,
        max_iter=1000
    ))
])

# Define parameter grid for meta learner
meta_params = {
    'rbf__gamma': [0.1, 1.0],
    'lr__C': [0.1, 1.0, 10.0]
}

print("--- Tuning Meta Learner ---")
meta_grid = GridSearchCV(
    estimator=meta_learner,
    param_grid=meta_params,
    cv=cv,
    scoring='roc_auc',
    n_jobs=-1,
    verbose=0
)

# Create stacking classifier
stacking_classifier = StackingClassifier(
    estimators=base_estimators,
    final_estimator=meta_grid,
    cv=cv,
    n_jobs=-1,
    verbose=0
)

print("--- Training Stacking Ensemble ---")
start_time = time.time()

# Train the stacking ensemble (NO TEST DATA USED HERE)
stacking_classifier.fit(X_train_final, y_train)

training_time = time.time() - start_time
print(f"✅ Stacking ensemble trained in {training_time:.2f}s")

# Access the tuned meta learner
best_meta_learner = stacking_classifier.final_estimator_.best_estimator_
print(f"🎯 Best meta learner score: {stacking_classifier.final_estimator_.best_score_:.4f}")
print(f"🔧 Best meta learner params: {stacking_classifier.final_estimator_.best_params_}")

# =============================================================================
# 6️⃣ FINAL EVALUATION ON TEST SET
# =============================================================================
print("\n" + "=" * 40)
print("📈 FINAL EVALUATION")
print("=" * 40)

# Evaluate stacking ensemble on test set
final_metrics = evaluate_model('STACKING_ENSEMBLE', stacking_classifier, X_test_final, y_test)

print(f"🎯 FINAL RESULTS:")
print(f"   Accuracy: {final_metrics['Accuracy']:.4f}")
print(f"   Precision: {final_metrics['Precision']:.4f}")
print(f"   Recall: {final_metrics['Recall']:.4f}")
print(f"   F1-Score: {final_metrics['F1']:.4f}")
print(f"   ROC-AUC: {final_metrics['ROC_AUC']:.4f}")
print(f"   G-Mean: {final_metrics['G_Mean']:.4f}")

# Save final metrics
final_metrics_df = pd.DataFrame([final_metrics])
final_metrics_df.to_csv('final_metrics.csv', index=False)
print("💾 Final metrics saved to 'final_metrics.csv'")

# =============================================================================
# 7️⃣ VISUALIZATIONS
# =============================================================================
print("\n" + "=" * 40)
print("📊 GENERATING VISUALIZATIONS")
print("=" * 40)

# Get predictions for visualizations
y_pred = stacking_classifier.predict(X_test_final)
y_pred_proba = stacking_classifier.predict_proba(X_test_final)[:, 1]

# ROC Curve
plt.figure(figsize=(10, 8))
fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
auc = roc_auc_score(y_test, y_pred_proba)

plt.subplot(2, 1, 1)
plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC Curve (AUC = {auc:.4f})')
plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random Classifier')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('ROC Curve - Stacking Ensemble')
plt.legend(loc="lower right")
plt.grid(True, alpha=0.3)

# Confusion Matrix
plt.subplot(2, 1, 2)
cm = confusion_matrix(y_test, y_pred)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', square=True)
plt.title('Confusion Matrix - Stacking Ensemble')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')

plt.tight_layout()
plt.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
plt.close()

# Separate confusion matrix plot
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', square=True,
            xticklabels=['Legitimate', 'Phishing'],
            yticklabels=['Legitimate', 'Phishing'])
plt.title('Confusion Matrix - Stacking Ensemble Classifier')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
plt.close()

print("✅ ROC curve saved to 'roc_curve.png'")
print("✅ Confusion matrix saved to 'confusion_matrix.png'")

# =============================================================================
# 8️⃣ FEATURE IMPORTANCES (OPTIONAL)
# =============================================================================
print("\n--- Extracting Feature Importances ---")
feature_importances = []

try:
    # Get feature importances from base models that support it
    for name, model in tuned_models.items():
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            for i, importance in enumerate(importances):
                feature_importances.append({
                    'Model': name.upper(),
                    'Feature': selected_features[i],
                    'Importance': importance
                })

    if feature_importances:
        feature_imp_df = pd.DataFrame(feature_importances)
        feature_imp_df.to_csv('feature_importances.csv', index=False)
        print("💾 Feature importances saved to 'feature_importances.csv'")
    else:
        print("ℹ️ No feature importances available from base models")

except Exception as e:
    print(f"⚠️ Could not extract feature importances: {e}")

# =============================================================================
# 9️⃣ SAVE FINAL MODEL
# =============================================================================
print("\n" + "=" * 40)
print("💾 SAVING FINAL MODEL")
print("=" * 40)

# Save the complete preprocessing pipeline and model
model_package = {
    'stacking_classifier': stacking_classifier,
    'target_encoder': target_encoder if categorical_cols else None,
    'variance_selector': variance_selector,
    'scaler': scaler,
    'feature_selector': selector,
    'selected_features': selected_features,
    'feature_names_after_var': feature_names_after_var,
    'categorical_cols': categorical_cols,
    'numerical_cols': numerical_cols,
    'num_imputer': num_imputer if numerical_cols else None,
    'cat_imputer': cat_imputer if categorical_cols else None
}

joblib.dump(model_package, 'stacking_ensemble.joblib')
print("✅ Complete model package saved to 'stacking_ensemble.joblib'")

# =============================================================================
# 🎉 SUMMARY
# =============================================================================
print("\n" + "=" * 60)
print("🎉 PHISHING DETECTION MODEL TRAINING COMPLETED!")
print("=" * 60)

print(f"""
📊 FINAL PERFORMANCE SUMMARY:
   🎯 Accuracy:  {final_metrics['Accuracy']:.4f}
   🎯 Precision: {final_metrics['Precision']:.4f}
   🎯 Recall:    {final_metrics['Recall']:.4f}
   🎯 F1-Score:  {final_metrics['F1']:.4f}
   🎯 ROC-AUC:   {final_metrics['ROC_AUC']:.4f}
   🎯 G-Mean:    {final_metrics['G_Mean']:.4f}

📁 OUTPUT FILES GENERATED:
   ✅ stacking_ensemble.joblib - Complete trained model
   ✅ base_metrics.csv - Base models performance
   ✅ final_metrics.csv - Final ensemble performance
   ✅ selected_features.csv - Selected features list
   ✅ descriptive_statistics.csv - Feature statistics
   ✅ roc_curve.png - ROC curve visualization
   ✅ confusion_matrix.png - Confusion matrix plot
   ✅ feature_importances.csv - Feature importance scores (if available)

🚀 MODEL IS READY FOR DEPLOYMENT!
""")

print("=" * 60)