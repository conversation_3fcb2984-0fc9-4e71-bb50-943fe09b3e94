"""
URL Feature Extraction Module for Phishing Detection

This module extracts exactly 30 features from URLs in the precise order required
by the trained model. It includes web scraping capabilities to extract content-based
features from actual websites.

Features extracted (in order):
1. LengthOfURL - Length of the URL string
2. URLComplexity - Complexity score based on special characters
3. TLD - Top Level Domain (categorical)
4. LetterCntInURL - Count of letters in URL
5. URLLetterRatio - Ratio of letters to total characters
6. DigitCntInURL - Count of digits in URL
7. URLDigitRatio - Ratio of digits to total characters
8. OtherSpclCharCntInURL - Count of other special characters
9. HavingPath - Whether URL has a path component
10. PathLength - Length of the path component
11. HasSSL - Whether URL uses HTTPS
12. LineOfCode - Number of lines in HTML source
13. LongestLineLength - Length of longest line in HTML
14. HasFavicon - Whether site has a favicon
15. HasRobotsBlocked - Whether robots.txt blocks crawling
16. IsSelfRedirects - Whether site redirects to itself
17. HasDescription - Whether page has meta description
18. HasSubmitButton - Whether page has submit buttons
19. HasCopyrightInfoKey  - Whether page has copyright info (note trailing space)
20. CntImages - Count of images on page
21. CntFilesCSS - Count of CSS files linked
22. CntFilesJS - Count of JavaScript files linked
23. CntSelfHRef - Count of self-referencing links
24. CntEmptyRef - Count of empty href attributes
25. CntExternalRef - Count of external references
26. CntIFrame - Count of iframe elements
27. UniqueFeatureCnt - Count of unique HTML features
28. ShannonEntropy - Shannon entropy of URL
29. KolmogorovComplexity - Kolmogorov complexity approximation
30. LikelinessIndex - Overall likelihood index
"""

import pandas as pd
import numpy as np
import requests
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import re
import math
import time
import logging
from typing import Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class URLFeatureExtractor:
    """
    Extracts 30 specific features from URLs for phishing detection.
    """
    
    def __init__(self, timeout: int = 10, max_retries: int = 2):
        """
        Initialize the feature extractor.
        
        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        
        # Set user agent to avoid blocking
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Common TLDs for categorical encoding
        self.common_tlds = [
            'com', 'org', 'net', 'edu', 'gov', 'mil', 'int', 'co', 'uk', 'de', 
            'fr', 'it', 'es', 'ru', 'cn', 'jp', 'br', 'au', 'ca', 'in', 'mx',
            'nl', 'se', 'no', 'dk', 'fi', 'pl', 'tr', 'gr', 'pt', 'cz', 'hu',
            'ro', 'bg', 'hr', 'sk', 'si', 'ee', 'lv', 'lt', 'ie', 'at', 'ch',
            'be', 'lu', 'is', 'mt', 'cy', 'info', 'biz', 'name', 'pro', 'museum',
            'aero', 'coop', 'jobs', 'mobi', 'travel', 'tel', 'asia', 'cat', 'xxx'
        ]
    
    def extract_features(self, url: str) -> pd.DataFrame:
        """
        Extract all 30 features from a URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            DataFrame with exactly 30 features in the correct order
        """
        try:
            # Ensure URL has protocol
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            
            # Parse URL
            parsed_url = urlparse(url)
            
            # Initialize features dictionary
            features = {}
            
            # Extract URL-based features (1-11)
            self._extract_url_features(url, parsed_url, features)
            
            # Extract content-based features (12-30)
            self._extract_content_features(url, parsed_url, features)
            
            # Create DataFrame with exact column order (matching the model's selected features)
            feature_names = [
                'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
                'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
                'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
                'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
                'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
                'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
                'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
            ]
            
            # Ensure all features are present
            for feature_name in feature_names:
                if feature_name not in features:
                    features[feature_name] = 0
            
            # Create DataFrame
            df = pd.DataFrame([features], columns=feature_names)
            
            logger.info(f"Successfully extracted features for URL: {url}")
            return df
            
        except Exception as e:
            logger.error(f"Error extracting features from {url}: {str(e)}")
            # Return DataFrame with default values
            return self._get_default_features()
    
    def _extract_url_features(self, url: str, parsed_url, features: Dict[str, Any]):
        """Extract features 1-11 based on URL structure."""
        
        # 1. LengthOfURL
        features['LengthOfURL'] = len(url)
        
        # 2. URLComplexity - based on variety of special characters
        special_chars = set(re.findall(r'[^a-zA-Z0-9]', url))
        features['URLComplexity'] = len(special_chars)
        
        # 3. TLD - Top Level Domain
        domain_parts = parsed_url.netloc.split('.')
        if len(domain_parts) > 1:
            tld = domain_parts[-1].lower()
            features['TLD'] = tld if tld in self.common_tlds else 'other'
        else:
            features['TLD'] = 'other'
        
        # 4. LetterCntInURL
        features['LetterCntInURL'] = len(re.findall(r'[a-zA-Z]', url))
        
        # 5. URLLetterRatio
        total_chars = len(url)
        features['URLLetterRatio'] = features['LetterCntInURL'] / total_chars if total_chars > 0 else 0
        
        # 6. DigitCntInURL
        features['DigitCntInURL'] = len(re.findall(r'[0-9]', url))
        
        # 7. URLDigitRatio
        features['URLDigitRatio'] = features['DigitCntInURL'] / total_chars if total_chars > 0 else 0
        
        # 8. OtherSpclCharCntInURL
        other_chars = len(url) - features['LetterCntInURL'] - features['DigitCntInURL']
        features['OtherSpclCharCntInURL'] = other_chars
        
        # 9. HavingPath
        features['HavingPath'] = 1 if parsed_url.path and parsed_url.path != '/' else 0
        
        # 10. PathLength
        features['PathLength'] = len(parsed_url.path) if parsed_url.path else 0
        
        # 11. HasSSL
        features['HasSSL'] = 1 if parsed_url.scheme == 'https' else 0

    def _extract_content_features(self, url: str, parsed_url, features: Dict[str, Any]):
        """Extract features 12-30 based on website content."""

        try:
            # Fetch website content
            html_content, response = self._fetch_content(url)

            if html_content:
                soup = BeautifulSoup(html_content, 'html.parser')

                # 12. LineOfCode - Number of lines in HTML
                features['LineOfCode'] = len(html_content.split('\n'))

                # 13. LongestLineLength - Length of longest line
                lines = html_content.split('\n')
                features['LongestLineLength'] = max(len(line) for line in lines) if lines else 0

                # 14. HasFavicon - Check for favicon
                features['HasFavicon'] = self._check_favicon(soup, url)

                # 15. HasRobotsBlocked - Check robots.txt
                features['HasRobotsBlocked'] = self._check_robots_blocked(parsed_url)

                # 16. IsSelfRedirects - Check for self redirects
                features['IsSelfRedirects'] = self._check_self_redirects(response, url)

                # 17. HasDescription - Check for meta description
                features['HasDescription'] = self._check_meta_description(soup)

                # 18. HasSubmitButton - Check for submit buttons
                features['HasSubmitButton'] = self._count_submit_buttons(soup)

                # 19. HasCopyrightInfoKey - Check for copyright info
                features['HasCopyrightInfoKey'] = self._check_copyright_info(soup)

                # 20. CntImages - Count images
                features['CntImages'] = len(soup.find_all('img'))

                # 21. CntFilesCSS - Count CSS files
                features['CntFilesCSS'] = self._count_css_files(soup)

                # 22. CntFilesJS - Count JavaScript files
                features['CntFilesJS'] = self._count_js_files(soup)

                # 23. CntSelfHRef - Count self-referencing links
                features['CntSelfHRef'] = self._count_self_href(soup, parsed_url)

                # 24. CntEmptyRef - Count empty href attributes
                features['CntEmptyRef'] = self._count_empty_href(soup)

                # 25. CntExternalRef - Count external references
                features['CntExternalRef'] = self._count_external_refs(soup, parsed_url)

                # 26. CntIFrame - Count iframe elements
                features['CntIFrame'] = len(soup.find_all('iframe'))

                # 27. UniqueFeatureCnt - Count unique HTML features
                features['UniqueFeatureCnt'] = self._count_unique_features(soup)

            else:
                # Set default values for content-based features if content fetch fails
                self._set_default_content_features(features)

            # 28. ShannonEntropy - Shannon entropy of URL
            features['ShannonEntropy'] = self._calculate_shannon_entropy(url)

            # 29. KolmogorovComplexity - Approximation using compression
            features['KolmogorovComplexity'] = self._calculate_kolmogorov_complexity(url)

            # 30. LikelinessIndex - Overall likelihood index
            features['LikelinessIndex'] = self._calculate_likelihood_index(features)

        except Exception as e:
            logger.error(f"Error extracting content features: {str(e)}")
            self._set_default_content_features(features)

    def _fetch_content(self, url: str) -> Tuple[Optional[str], Optional[requests.Response]]:
        """Fetch HTML content from URL with retries."""

        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
                response.raise_for_status()

                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' in content_type:
                    return response.text, response
                else:
                    logger.warning(f"Non-HTML content type: {content_type}")
                    return None, response

            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt < self.max_retries:
                    time.sleep(1)  # Wait before retry
                else:
                    logger.error(f"Failed to fetch content after {self.max_retries + 1} attempts")

        return None, None

    def _check_favicon(self, soup: BeautifulSoup, url: str) -> int:
        """Check if website has a favicon."""

        # Check for favicon in HTML
        favicon_links = soup.find_all('link', rel=lambda x: x and 'icon' in x.lower())
        if favicon_links:
            return 1

        # Check for default favicon.ico
        try:
            parsed_url = urlparse(url)
            favicon_url = f"{parsed_url.scheme}://{parsed_url.netloc}/favicon.ico"
            response = self.session.head(favicon_url, timeout=5)
            return 1 if response.status_code == 200 else 0
        except:
            return 0

    def _check_robots_blocked(self, parsed_url) -> int:
        """Check if robots.txt blocks crawling."""

        try:
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            response = self.session.get(robots_url, timeout=5)

            if response.status_code == 200:
                robots_content = response.text.lower()
                # Simple check for disallow directives
                if 'disallow:' in robots_content and ('/' in robots_content or '*' in robots_content):
                    return 1
            return 0
        except:
            return 0

    def _check_self_redirects(self, response: Optional[requests.Response], original_url: str) -> int:
        """Check if the URL redirects to itself."""

        if not response or not response.history:
            return 0

        try:
            original_domain = urlparse(original_url).netloc.lower()
            final_domain = urlparse(response.url).netloc.lower()

            # Check if redirected to same domain
            return 1 if original_domain == final_domain and len(response.history) > 0 else 0
        except:
            return 0

    def _check_meta_description(self, soup: BeautifulSoup) -> int:
        """Check if page has meta description."""

        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content', '').strip():
            return 1
        return 0

    def _count_submit_buttons(self, soup: BeautifulSoup) -> int:
        """Count submit buttons on the page."""

        submit_buttons = 0

        # Count input type="submit"
        submit_buttons += len(soup.find_all('input', type='submit'))

        # Count button type="submit"
        submit_buttons += len(soup.find_all('button', type='submit'))

        # Count buttons with submit-like text
        buttons = soup.find_all('button')
        for button in buttons:
            button_text = button.get_text().lower().strip()
            if any(word in button_text for word in ['submit', 'send', 'login', 'sign in', 'register']):
                submit_buttons += 1

        return submit_buttons

    def _check_copyright_info(self, soup: BeautifulSoup) -> int:
        """Check if page has copyright information."""

        # Look for copyright symbols and text
        page_text = soup.get_text().lower()
        copyright_indicators = ['©', 'copyright', '(c)', 'all rights reserved']

        for indicator in copyright_indicators:
            if indicator in page_text:
                return 1

        return 0

    def _count_css_files(self, soup: BeautifulSoup) -> int:
        """Count CSS files linked in the page."""

        css_count = 0

        # Count link tags with rel="stylesheet"
        css_links = soup.find_all('link', rel='stylesheet')
        css_count += len(css_links)

        # Count style tags
        css_count += len(soup.find_all('style'))

        return css_count

    def _count_js_files(self, soup: BeautifulSoup) -> int:
        """Count JavaScript files linked in the page."""

        js_scripts = soup.find_all('script', src=True)
        return len(js_scripts)

    def _count_self_href(self, soup: BeautifulSoup, parsed_url) -> int:
        """Count self-referencing links."""

        self_refs = 0
        domain = parsed_url.netloc.lower()

        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href'].lower()

            # Check for self-references
            if href.startswith('#') or href == '/' or href == './':
                self_refs += 1
            elif domain in href:
                self_refs += 1

        return self_refs

    def _count_empty_href(self, soup: BeautifulSoup) -> int:
        """Count empty href attributes."""

        empty_refs = 0
        links = soup.find_all('a', href=True)

        for link in links:
            href = link['href'].strip()
            if not href or href == '#' or href == 'javascript:void(0)':
                empty_refs += 1

        return empty_refs

    def _count_external_refs(self, soup: BeautifulSoup, parsed_url) -> int:
        """Count external references."""

        external_refs = 0
        domain = parsed_url.netloc.lower()

        # Count external links
        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href'].lower()
            if href.startswith('http') and domain not in href:
                external_refs += 1

        # Count external scripts
        scripts = soup.find_all('script', src=True)
        for script in scripts:
            src = script['src'].lower()
            if src.startswith('http') and domain not in src:
                external_refs += 1

        # Count external CSS
        css_links = soup.find_all('link', rel='stylesheet', href=True)
        for css_link in css_links:
            href = css_link['href'].lower()
            if href.startswith('http') and domain not in href:
                external_refs += 1

        return external_refs

    def _count_unique_features(self, soup: BeautifulSoup) -> int:
        """Count unique HTML features/tags."""

        unique_tags = set()

        for tag in soup.find_all():
            unique_tags.add(tag.name)

        return len(unique_tags)

    def _calculate_shannon_entropy(self, url: str) -> float:
        """Calculate Shannon entropy of URL."""

        if not url:
            return 0.0

        # Count character frequencies
        char_counts = {}
        for char in url:
            char_counts[char] = char_counts.get(char, 0) + 1

        # Calculate entropy
        entropy = 0.0
        url_length = len(url)

        for count in char_counts.values():
            probability = count / url_length
            if probability > 0:
                entropy -= probability * math.log2(probability)

        return entropy

    def _calculate_kolmogorov_complexity(self, url: str) -> float:
        """Approximate Kolmogorov complexity using compression ratio."""

        try:
            import zlib

            if not url:
                return 0.0

            # Compress the URL
            compressed = zlib.compress(url.encode('utf-8'))

            # Calculate compression ratio as approximation
            complexity = len(compressed) / len(url) if len(url) > 0 else 0

            return complexity

        except Exception:
            # Fallback: use character variety as complexity measure
            unique_chars = len(set(url))
            total_chars = len(url)
            return unique_chars / total_chars if total_chars > 0 else 0

    def _calculate_likelihood_index(self, features: Dict[str, Any]) -> float:
        """Calculate overall likelihood index based on extracted features."""

        # Simple heuristic combining multiple features
        score = 0.0

        # URL-based indicators
        if features.get('HasSSL', 0) == 1:
            score += 0.2

        if features.get('LengthOfURL', 0) < 100:  # Reasonable length
            score += 0.1

        if features.get('URLComplexity', 0) < 10:  # Not too complex
            score += 0.1

        # Content-based indicators
        if features.get('HasFavicon', 0) == 1:
            score += 0.1

        if features.get('HasDescription', 0) == 1:
            score += 0.1

        if features.get('HasCopyrightInfoKey ', 0) == 1:
            score += 0.1

        # Normalize to 0-1 range
        return min(score, 1.0)

    def _set_default_content_features(self, features: Dict[str, Any]):
        """Set default values for content-based features when content fetch fails."""

        content_features = [
            'LineOfCode', 'LongestLineLength', 'HasFavicon', 'HasRobotsBlocked',
            'IsSelfRedirects', 'HasDescription', 'HasSubmitButton', 'HasCopyrightInfoKey',
            'CntImages', 'CntFilesCSS', 'CntFilesJS', 'CntSelfHRef', 'CntEmptyRef',
            'CntExternalRef', 'CntIFrame', 'UniqueFeatureCnt'
        ]

        for feature in content_features:
            if feature not in features:
                features[feature] = 0

    def _get_default_features(self) -> pd.DataFrame:
        """Return DataFrame with default feature values."""

        feature_names = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]

        # Create default values
        default_values = {}
        for feature in feature_names:
            if feature == 'TLD':
                default_values[feature] = 'other'
            else:
                default_values[feature] = 0

        return pd.DataFrame([default_values], columns=feature_names)


# Example usage and testing
if __name__ == "__main__":
    # Test the feature extractor
    extractor = URLFeatureExtractor()

    # Test URLs
    test_urls = [
        "https://www.google.com",
        "http://example.com/path/to/page",
        "https://github.com/user/repo"
    ]

    for url in test_urls:
        print(f"\nTesting URL: {url}")
        features_df = extractor.extract_features(url)
        print(f"Features shape: {features_df.shape}")
        print(f"Feature names: {list(features_df.columns)}")
        print(f"Sample values: {features_df.iloc[0].head(10).to_dict()}")
