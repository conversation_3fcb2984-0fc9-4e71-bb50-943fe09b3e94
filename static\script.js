/**
 * PhishGuard AI - Frontend JavaScript
 * Handles user interactions, API calls, and UI updates
 */

class PhishGuardApp {
    constructor() {
        this.apiBaseUrl = '';
        this.isAnalyzing = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupExampleUrls();
    }

    bindEvents() {
        // Form submission
        const urlForm = document.getElementById('urlForm');
        const urlInput = document.getElementById('urlInput');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const clearBtn = document.getElementById('clearBtn');

        urlForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.analyzeUrl();
        });

        // Input events
        urlInput.addEventListener('input', (e) => {
            this.toggleClearButton(e.target.value);
            this.hideResults();
        });

        urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !this.isAnalyzing) {
                this.analyzeUrl();
            }
        });

        // Clear button
        clearBtn.addEventListener('click', () => {
            this.clearInput();
        });

        // Analyze button
        analyzeBtn.addEventListener('click', () => {
            if (!this.isAnalyzing) {
                this.analyzeUrl();
            }
        });
    }

    setupExampleUrls() {
        const exampleItems = document.querySelectorAll('.example-item');
        exampleItems.forEach(item => {
            item.addEventListener('click', () => {
                const url = item.getAttribute('data-url');
                this.setInputValue(url);
                this.analyzeUrl();
            });
        });
    }

    toggleClearButton(value) {
        const clearBtn = document.getElementById('clearBtn');
        if (value.trim()) {
            clearBtn.classList.add('visible');
        } else {
            clearBtn.classList.remove('visible');
        }
    }

    clearInput() {
        const urlInput = document.getElementById('urlInput');
        urlInput.value = '';
        urlInput.focus();
        this.toggleClearButton('');
        this.hideResults();
    }

    setInputValue(url) {
        const urlInput = document.getElementById('urlInput');
        urlInput.value = url;
        this.toggleClearButton(url);
    }

    async analyzeUrl() {
        const urlInput = document.getElementById('urlInput');
        const url = urlInput.value.trim();

        if (!url) {
            this.showToast('Please enter a URL to analyze', 'error');
            urlInput.focus();
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('Please enter a valid URL', 'error');
            urlInput.focus();
            return;
        }

        this.showLoading();
        this.hideResults();

        try {
            const response = await fetch('/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            });

            const data = await response.json();

            if (response.ok) {
                this.showResults(data);
                this.showToast('Analysis completed successfully', 'success');
            } else {
                throw new Error(data.detail || 'Analysis failed');
            }
        } catch (error) {
            console.error('Error analyzing URL:', error);
            this.showToast(`Error: ${error.message}`, 'error');
            this.showErrorResults(url, error.message);
        } finally {
            this.hideLoading();
        }
    }

    isValidUrl(string) {
        try {
            // Add protocol if missing
            let url = string;
            if (!url.match(/^https?:\/\//)) {
                url = 'http://' + url;
            }
            new URL(url);
            return true;
        } catch (_) {
            return false;
        }
    }

    showLoading() {
        this.isAnalyzing = true;
        const loadingIndicator = document.getElementById('loadingIndicator');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const btnText = analyzeBtn.querySelector('.btn-text');
        const btnIcon = analyzeBtn.querySelector('.btn-icon');

        loadingIndicator.classList.remove('hidden');
        analyzeBtn.disabled = true;
        btnText.textContent = 'Analyzing...';
        btnIcon.className = 'fas fa-spinner fa-spin btn-icon';
    }

    hideLoading() {
        this.isAnalyzing = false;
        const loadingIndicator = document.getElementById('loadingIndicator');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const btnText = analyzeBtn.querySelector('.btn-text');
        const btnIcon = analyzeBtn.querySelector('.btn-icon');

        loadingIndicator.classList.add('hidden');
        analyzeBtn.disabled = false;
        btnText.textContent = 'Analyze URL';
        btnIcon.className = 'fas fa-search btn-icon';
    }

    showResults(data) {
        const resultsSection = document.getElementById('resultsSection');
        const resultContent = document.getElementById('resultContent');

        const isLegitimate = data.prediction === 0;
        const resultClass = isLegitimate ? 'legitimate' : 'phishing';
        const resultIcon = isLegitimate ? 'fas fa-shield-check' : 'fas fa-exclamation-triangle';
        const resultTitle = isLegitimate ? 'Safe Website' : 'Potential Phishing Site';
        const resultMessage = isLegitimate ? 
            'This URL appears to be legitimate and safe to visit.' : 
            'This URL shows characteristics of a phishing site. Exercise caution.';

        resultContent.innerHTML = `
            <div class="result-card ${resultClass}">
                <div class="result-icon">
                    <i class="${resultIcon}"></i>
                </div>
                <div class="result-title">${resultTitle}</div>
                <div class="result-url">${data.url}</div>
                <p>${resultMessage}</p>
                
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Prediction</div>
                        <div class="detail-value">${data.prediction_label}</div>
                    </div>
                    ${data.confidence ? `
                    <div class="detail-item">
                        <div class="detail-label">Confidence</div>
                        <div class="detail-value">${(data.confidence * 100).toFixed(1)}%</div>
                    </div>
                    ` : ''}
                    <div class="detail-item">
                        <div class="detail-label">Processing Time</div>
                        <div class="detail-value">${data.processing_time_ms.toFixed(0)}ms</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Features Extracted</div>
                        <div class="detail-value">${data.features_extracted ? 'Yes' : 'No'}</div>
                    </div>
                </div>
            </div>
        `;

        resultsSection.classList.remove('hidden');
        resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    showErrorResults(url, errorMessage) {
        const resultsSection = document.getElementById('resultsSection');
        const resultContent = document.getElementById('resultContent');

        resultContent.innerHTML = `
            <div class="result-card phishing">
                <div class="result-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="result-title">Analysis Failed</div>
                <div class="result-url">${url}</div>
                <p>Unable to analyze this URL. ${errorMessage}</p>
                
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Status</div>
                        <div class="detail-value">Error</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Recommendation</div>
                        <div class="detail-value">Exercise Caution</div>
                    </div>
                </div>
            </div>
        `;

        resultsSection.classList.remove('hidden');
        resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    hideResults() {
        const resultsSection = document.getElementById('resultsSection');
        resultsSection.classList.add('hidden');
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = type === 'error' ? 'fas fa-exclamation-circle' : 
                    type === 'success' ? 'fas fa-check-circle' : 
                    'fas fa-info-circle';
        
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PhishGuardApp();
});

// Add some additional utility functions
window.addEventListener('load', () => {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('urlInput').focus();
        }
        
        // Escape to clear input
        if (e.key === 'Escape') {
            const urlInput = document.getElementById('urlInput');
            if (document.activeElement === urlInput) {
                urlInput.blur();
            }
        }
    });
});

// Add error handling for uncaught errors
window.addEventListener('error', (e) => {
    console.error('Uncaught error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});
