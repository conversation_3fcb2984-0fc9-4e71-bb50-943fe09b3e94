"""
Data Preprocessing Pipeline for Phishing Detection

This module implements the exact preprocessing pipeline used during model training,
but WITHOUT feature selection steps as requested. The pipeline includes:

1. Missing value imputation (median for numeric, most frequent for categorical)
2. Target encoding for categorical features
3. Standard scaling for all numeric features

The preprocessing maintains the exact feature order and naming expected by the model.
"""

import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from category_encoders import TargetEncoder
import joblib
import logging
from typing import Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """
    Preprocesses feature data for phishing detection model.
    
    This class applies the exact same preprocessing steps used during training:
    1. Missing value imputation
    2. Target encoding for categorical features  
    3. Standard scaling
    
    Note: Feature selection is NOT included as per user requirements.
    """
    
    def __init__(self):
        """Initialize the preprocessor."""
        self.num_imputer = None
        self.cat_imputer = None
        self.target_encoder = None
        self.scaler = None
        self.categorical_cols = []
        self.numerical_cols = []
        self.is_fitted = False
        
        # Expected feature names in exact order (matching the model's selected features)
        self.expected_features = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]
    
    def fit(self, X: pd.DataFrame, y: Optional[pd.Series] = None):
        """
        Fit the preprocessor on training data.
        
        Args:
            X: Feature DataFrame with exact column names
            y: Target series (required for target encoding)
        """
        try:
            logger.info("Fitting preprocessor...")
            
            # Validate input
            self._validate_features(X)
            
            # Identify categorical and numerical columns
            self.categorical_cols = X.select_dtypes(include=['object', 'category']).columns.tolist()
            self.numerical_cols = X.select_dtypes(include=[np.number]).columns.tolist()
            
            logger.info(f"Categorical columns: {self.categorical_cols}")
            logger.info(f"Numerical columns: {self.numerical_cols}")
            
            # 1. Fit missing value imputers
            if self.numerical_cols:
                self.num_imputer = SimpleImputer(strategy='median')
                self.num_imputer.fit(X[self.numerical_cols])
                logger.info("✅ Numerical imputer fitted")
            
            if self.categorical_cols:
                self.cat_imputer = SimpleImputer(strategy='most_frequent')
                self.cat_imputer.fit(X[self.categorical_cols])
                logger.info("✅ Categorical imputer fitted")
            
            # 2. Fit target encoder for categorical features
            if self.categorical_cols and y is not None:
                self.target_encoder = TargetEncoder(cols=self.categorical_cols, smoothing=1.0)
                # Create a copy for fitting to avoid modifying original data
                X_copy = X.copy()
                if self.num_imputer:
                    X_copy[self.numerical_cols] = self.num_imputer.transform(X_copy[self.numerical_cols])
                if self.cat_imputer:
                    X_copy[self.categorical_cols] = self.cat_imputer.transform(X_copy[self.categorical_cols])
                
                self.target_encoder.fit(X_copy, y)
                logger.info("✅ Target encoder fitted")
            
            # 3. Fit scaler on all features after encoding
            X_processed = self._apply_imputation_and_encoding(X, y)
            self.scaler = StandardScaler()
            self.scaler.fit(X_processed)
            logger.info("✅ Standard scaler fitted")
            
            self.is_fitted = True
            logger.info("✅ Preprocessor fitting completed successfully")
            
        except Exception as e:
            logger.error(f"Error fitting preprocessor: {str(e)}")
            raise
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using fitted preprocessor.
        
        Args:
            X: Feature DataFrame to transform
            
        Returns:
            Transformed DataFrame
        """
        try:
            if not self.is_fitted:
                raise ValueError("Preprocessor must be fitted before transform")
            
            logger.info("Transforming features...")
            
            # Validate input
            self._validate_features(X)
            
            # Apply preprocessing steps
            X_processed = self._apply_imputation_and_encoding(X)
            
            # Apply scaling
            X_scaled = self.scaler.transform(X_processed)
            
            # Convert back to DataFrame with original column names
            # After target encoding, categorical columns become numeric
            result_df = pd.DataFrame(X_scaled, columns=self.expected_features, index=X.index)
            
            logger.info("✅ Features transformed successfully")
            return result_df
            
        except Exception as e:
            logger.error(f"Error transforming features: {str(e)}")
            raise
    
    def fit_transform(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> pd.DataFrame:
        """
        Fit the preprocessor and transform the data.
        
        Args:
            X: Feature DataFrame
            y: Target series
            
        Returns:
            Transformed DataFrame
        """
        self.fit(X, y)
        return self.transform(X)
    
    def _apply_imputation_and_encoding(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> pd.DataFrame:
        """Apply imputation and target encoding steps."""
        
        X_processed = X.copy()
        
        # 1. Apply missing value imputation
        if self.num_imputer and self.numerical_cols:
            X_processed[self.numerical_cols] = self.num_imputer.transform(X_processed[self.numerical_cols])
        
        if self.cat_imputer and self.categorical_cols:
            X_processed[self.categorical_cols] = self.cat_imputer.transform(X_processed[self.categorical_cols])
        
        # 2. Apply target encoding for categorical features
        if self.target_encoder and self.categorical_cols:
            X_processed = self.target_encoder.transform(X_processed)
        
        return X_processed
    
    def _validate_features(self, X: pd.DataFrame):
        """Validate that input DataFrame has expected features."""
        
        missing_features = set(self.expected_features) - set(X.columns)
        if missing_features:
            raise ValueError(f"Missing expected features: {missing_features}")
        
        extra_features = set(X.columns) - set(self.expected_features)
        if extra_features:
            logger.warning(f"Extra features found (will be ignored): {extra_features}")
        
        # Ensure correct column order
        X_reordered = X[self.expected_features]
        return X_reordered
    
    def save(self, filepath: str):
        """Save the fitted preprocessor to file."""
        
        if not self.is_fitted:
            raise ValueError("Cannot save unfitted preprocessor")
        
        preprocessor_data = {
            'num_imputer': self.num_imputer,
            'cat_imputer': self.cat_imputer,
            'target_encoder': self.target_encoder,
            'scaler': self.scaler,
            'categorical_cols': self.categorical_cols,
            'numerical_cols': self.numerical_cols,
            'expected_features': self.expected_features,
            'is_fitted': self.is_fitted
        }
        
        joblib.dump(preprocessor_data, filepath)
        logger.info(f"✅ Preprocessor saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str):
        """Load a fitted preprocessor from file."""
        
        preprocessor_data = joblib.load(filepath)
        
        preprocessor = cls()
        preprocessor.num_imputer = preprocessor_data['num_imputer']
        preprocessor.cat_imputer = preprocessor_data['cat_imputer']
        preprocessor.target_encoder = preprocessor_data['target_encoder']
        preprocessor.scaler = preprocessor_data['scaler']
        preprocessor.categorical_cols = preprocessor_data['categorical_cols']
        preprocessor.numerical_cols = preprocessor_data['numerical_cols']
        preprocessor.expected_features = preprocessor_data['expected_features']
        preprocessor.is_fitted = preprocessor_data['is_fitted']
        
        logger.info(f"✅ Preprocessor loaded from {filepath}")
        return preprocessor


# Example usage
if __name__ == "__main__":
    # Test the preprocessor
    from url_feature_extractor import URLFeatureExtractor
    
    # Extract features from a test URL
    extractor = URLFeatureExtractor()
    features_df = extractor.extract_features("https://www.google.com")
    
    print(f"Original features shape: {features_df.shape}")
    print(f"Feature columns: {list(features_df.columns)}")
    
    # Test preprocessor (without target for transform-only)
    preprocessor = DataPreprocessor()
    
    # For testing, we'll create dummy target data
    dummy_target = pd.Series([0], index=features_df.index)
    
    # Fit and transform
    processed_features = preprocessor.fit_transform(features_df, dummy_target)
    
    print(f"Processed features shape: {processed_features.shape}")
    print(f"Processed feature columns: {list(processed_features.columns)}")
    print(f"Sample processed values: {processed_features.iloc[0].head(10).to_dict()}")
